using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class TestTextMeshmaterialchange : MonoBehaviour
{
    public string text;
    public TextMeshProUGUI textMeshProUGUI;
    public Material materialShader;
    public TMP_FontAsset originalFont;

    [ContextMenu("Apply Shake Submaterial")]
    void ApplyShakeSubmaterial()
    {
        // 1) Definir o array de materiais que o TMP irá usar
        var baseMat = textMeshProUGUI.fontMaterial;  // material 0
        textMeshProUGUI.fontSharedMaterials = new Material[] { baseMat, materialShader };

        // 2) Preparar o texto usando a tag <material=1> para o trecho com shake
        //    tudo fora da tag fica com o material 0
        textMeshProUGUI.text = $"Normal <material=1>{text}</material>";
        textMeshProUGUI.SetText("Normal <material=1>carai um texto</material>");

        // 3) Forçar atualização imediata das malhas (inclui sub-meshes)
        textMeshProUGUI.ForceMeshUpdate();
        
        Debug.Log("Material shared aplicado: Count = " + textMeshProUGUI.fontSharedMaterials.Length);
    }
}
