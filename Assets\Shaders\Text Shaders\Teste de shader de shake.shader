Shader "TextMeshPro/Distance Field/Shake"
{
    Properties
    {
        _FaceColor ("Face Color", Color) = (1,1,1,1)
        _MainTex ("Font Atlas", 2D) = "white" {}
        _ShakeAmount ("Shake Amount", Range(0, 10)) = 1
        _ShakeSpeed ("Shake Speed", Range(0, 20)) = 10
    }
    
    SubShader
    {
        Tags { "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" }
        Lighting Off
        Cull Off
        ZTest Always
        ZWrite Off
        Blend SrcAlpha OneMinusSrcAlpha
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile __ UNITY_UI_CLIP_RECT
            #include "UnityCG.cginc"
            #include "UnityUI.cginc"
            
            struct appdata_t
            {
                float4 vertex : POSITION;
                float4 color : COLOR;
                float2 texcoord : TEXCOORD0;
            };
            
            struct v2f
            {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
                float2 texcoord : TEXCOORD0;
                float4 worldPosition : TEXCOORD1;
            };
            
            sampler2D _MainTex;
            fixed4 _FaceColor;
            float _ShakeAmount;
            float _ShakeSpeed;
            float4 _ClipRect;
            
            v2f vert(appdata_t v)
            {
                v2f OUT;
                
                // Aplicar tremedeira
                float2 shake = float2(
                    sin(_Time.y * _ShakeSpeed + v.vertex.x * 10) * _ShakeAmount * 0.001,
                    cos(_Time.y * _ShakeSpeed * 1.3 + v.vertex.y * 10) * _ShakeAmount * 0.001
                );
                
                v.vertex.xy += shake;
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);
                OUT.texcoord = v.texcoord;
                OUT.color = v.color * _FaceColor;
                return OUT;
            }
            
            fixed4 frag(v2f IN) : SV_Target
            {
                // Sample da textura SDF do TextMeshPro
                float4 texSample = tex2D(_MainTex, IN.texcoord);
                
                // Para TextMeshPro SDF, usar o canal correto (geralmente .a ou .r)
                float sdf = texSample.a;
                
                // Aplicar threshold para SDF
                float alpha = smoothstep(0.4, 0.6, sdf);
                
                half4 color = IN.color;
                color.a *= alpha;
                
                #ifdef UNITY_UI_CLIP_RECT
                color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                #endif
                
                clip(color.a - 0.001);
                return color;
            }
            ENDCG
        }
    }
}